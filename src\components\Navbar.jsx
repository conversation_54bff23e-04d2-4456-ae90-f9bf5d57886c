import React, { useState, useEffect } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Modal,
    TouchableWithoutFeedback,
} from 'react-native';

import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchBusinessDay } from '../apiHandling/businessDayAPI';

const Navbar = () => {
    const [currentTime, setCurrentTime] = useState('');
    const [selectedMenu, setSelectedMenu] = useState('');
    const [selectedSubOption, setSelectedSubOption] = useState('');
    const [showDropdown, setShowDropdown] = useState(false);
    const [showExitModal, setShowExitModal] = useState(false);
    const [expandedStates, setExpandedStates] = useState({});
    const [dropdownPosition] = useState({ top: 100, left: 20 });
    const [businessDate, setBusinessDate] = useState('Loading...');
    const [branchId, setBranchId] = useState('');
    const [branchName, setBranchName] = useState('');
    const [activeDropdownMenu, setActiveDropdownMenu] = useState('');

    const navigation = useNavigation();
    const route = useRoute();

    const loadBranchInfo = async () => {
        try {
            const storedBranch = await AsyncStorage.getItem('selectedBranch');
            if (storedBranch) {
                const parsedBranch = JSON.parse(storedBranch);
                setBranchId(parsedBranch.BranchId || 'N/A');
                setBranchName(parsedBranch.BranchName || 'N/A');
            } else {
                setBranchId('N/A');
                setBranchName('N/A');
            }
        } catch (error) {
            console.error('Error loading branch info:', error);
            setBranchId('N/A');
            setBranchName('N/A');
        }
    };

    const getBusinessDay = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');

            if (bearerToken && selectedBranch) {
                const branch = JSON.parse(selectedBranch);
                const branchId = branch.BranchId;

                const formattedDate = await fetchBusinessDay(bearerToken, branchId);
                setBusinessDate(formattedDate);
            } else {
                console.error('Missing token or branch data');
                setBusinessDate('Date not available');
            }
        } catch (error) {
            console.error('Error in getBusinessDay:', error);
            setBusinessDate(new Date().toLocaleDateString());
        }
    };

    const menuOptions = [
        'Billing',
        'Stock',
        'Logistics',
        'Finance',
        'HR',
        'Utils',
        'Reports',
        'Exit',
    ];

    const stockDropdownOptions = [
        { title: 'Receiving', subOptions: ['Receiving', 'Wheat', 'Sugar'] },
        { title: 'Finished Goods', subOptions: ['Basmati Rice', 'Bread', 'Cookies'] },
        { title: 'Packaging', subOptions: ['Bags', 'Labels'] },
        'Stock Take',
        'Wastage',
    ];

    const financeDropdownOptions = [
      'Bank Transfer',
      'HO Transfer',
      'IB Branch Transfer',
      'Payment',
    
    ];

    const reportDropdownOptions = [
        'Day Rate',
        'Day Report',
        'Ledger',
        'Stock Report',
    ];

    const logisticsOptions = [
        'Trip',
        'Trip Roaster',
        'Trip Plan',
        'Delivery',
        'Trip Expenses',
        'Dayend',
        { title: 'Receipt', subOptions: ['Receipt For Subscription', 'Misc Collection'] },
    ];

    const allStockSubOptions = stockDropdownOptions.flatMap(opt => opt.subOptions);

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date().toLocaleTimeString());
        }, 1000);
        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        loadBranchInfo().then(getBusinessDay);
    }, []);

    useEffect(() => {
        if (route.name) {
            if (allStockSubOptions.includes(route.name)) {
                setSelectedMenu('Stock');
                setSelectedSubOption(route.name);
            } else if (logisticsOptions.some(opt =>
                typeof opt === 'string' ? opt === route.name : opt.subOptions?.includes(route.name)
            )) {
                setSelectedMenu('Logistics');
                setSelectedSubOption(route.name);
            } else if (financeDropdownOptions.includes(route.name)) {
                setSelectedMenu('Finance');
                setSelectedSubOption(route.name);
            } else if (reportDropdownOptions.includes(route.name)) {
                setSelectedMenu('Reports');
                setSelectedSubOption(route.name);
            } else {
                setSelectedMenu(route.name);
                setSelectedSubOption('');
            }
        }
    }, [route.name]);

    const handleMenuPress = (option) => {
        if (option === 'Exit') {
            setShowExitModal(true);
        } else if (option === 'Stock') {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setActiveDropdownMenu('Stock');
            setShowDropdown(true);
        } else if (option === 'Logistics') {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setActiveDropdownMenu('Logistics');
            setShowDropdown(true);
        } else if (option === 'Finance') {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setActiveDropdownMenu('Finance');
            setShowDropdown(true);
        } else if (option === 'Reports') {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setActiveDropdownMenu('Reports');
            setShowDropdown(true);
        } else {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setShowDropdown(false);
            navigation.navigate(option);
        }
    };

    const handleOptionPress = (option) => {
        setExpandedStates(prev => ({
            ...prev,
            [option.title]: !prev[option.title],
        }));
    };

    const handleSubOptionPress = (sub) => {
        setSelectedMenu(activeDropdownMenu);
        setSelectedSubOption(sub);
        setShowDropdown(false);
        navigation.navigate(sub);
    };

    const handleLogisticsOptionPress = (option) => {
        setSelectedMenu('Logistics');
        setSelectedSubOption(option);
        setShowDropdown(false);
        navigation.navigate(option);
    };

    // Handle logistics option press for objects
    const handleLogisticsOptionWithSubOptions = (option) => {
        setExpandedStates(prev => ({
            ...prev,
            [option.title]: !prev[option.title],
        }));
    };

    return (
        <View style={styles.container}>
            <View style={styles.appBar}>
                <View style={styles.topRow}>
                    <Text style={styles.topText}>{businessDate}</Text>
                    <Text style={styles.topText}>{currentTime}</Text>
                    <Text style={styles.topText}>{`${branchId}, ${branchName}`}</Text>
                </View>

                <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.menuRow}>
                    {menuOptions.map(option => {
                        const isSelected = selectedMenu === option;
                        const isExit = option === 'Exit';
                        return (
                            <TouchableOpacity
                                key={option}
                                onPress={() => handleMenuPress(option)}
                                style={[styles.menuButton, {
                                    backgroundColor: isSelected
                                        ? isExit ? '#FF3333' : '#FDC500'
                                        : isExit ? '#FF3333' : '#02096A'
                                }]}
                            >
                                <Text style={[styles.menuText, { color: isSelected ? 'black' : 'white' }]}>{option}</Text>
                            </TouchableOpacity>
                        );
                    })}
                </ScrollView>
            </View>

            {/* Dropdown Modal for both Stock and Logistics */}
            <Modal transparent visible={showDropdown} animationType="fade">
                <TouchableWithoutFeedback onPress={() => setShowDropdown(false)}>
                    <View style={styles.overlay}>
                        <TouchableWithoutFeedback>
                            <View style={[styles.dropdownContainer, dropdownPosition]}>
                                <ScrollView>
                                    {activeDropdownMenu === 'Stock' && stockDropdownOptions.map(option => (
                                        <View key={option.title}>
                                            <TouchableOpacity onPress={() => handleOptionPress(option)} style={styles.dropdownItem}>
                                                <Text style={styles.dropdownText}>{option.title}</Text>
                                                <Text style={styles.expandArrow}>{expandedStates[option.title] ? '▲' : '▼'}</Text>
                                            </TouchableOpacity>
                                            {expandedStates[option.title] && option.subOptions.map(sub => (
                                                <TouchableOpacity key={sub} onPress={() => handleSubOptionPress(sub)} style={styles.subOption}>
                                                    <Text style={styles.subOptionText}>{sub}</Text>
                                                </TouchableOpacity>
                                            ))}
                                        </View>
                                    ))}

                                    {activeDropdownMenu === 'Logistics' && logisticsOptions.map((option) => (
                                        <View key={typeof option === 'string' ? option : option.title}>
                                            {typeof option === 'string' ? (
                                                // Handle string options
                                                <TouchableOpacity
                                                    onPress={() => handleLogisticsOptionPress(option)}
                                                    style={styles.logisticsOption}
                                                >
                                                    <Text style={styles.logisticsOptionText}>{option}</Text>
                                                </TouchableOpacity>
                                            ) : (
                                                // Handle object options with subOptions
                                                <View>
                                                    <TouchableOpacity
                                                        onPress={() => handleLogisticsOptionWithSubOptions(option)}
                                                        style={styles.dropdownItem}
                                                    >
                                                        <Text style={styles.dropdownText}>{option.title}</Text>
                                                        <Text style={styles.expandArrow}>
                                                            {expandedStates[option.title] ? '▲' : '▼'}
                                                        </Text>
                                                    </TouchableOpacity>
                                                    {expandedStates[option.title] && option.subOptions.map(sub => (
                                                        <TouchableOpacity
                                                            key={sub}
                                                            onPress={() => handleSubOptionPress(sub)}
                                                            style={styles.subOption}
                                                        >
                                                            <Text style={styles.subOptionText}>{sub}</Text>
                                                        </TouchableOpacity>
                                                    ))}
                                                </View>
                                            )}
                                        </View>
                                    ))}

                                    {activeDropdownMenu === 'Finance' && financeDropdownOptions.map(option => (
                                        <TouchableOpacity
                                            key={option}
                                            onPress={() => handleLogisticsOptionPress(option)}
                                            style={styles.logisticsOption}
                                        >
                                            <Text style={styles.logisticsOptionText}>{option}</Text>
                                        </TouchableOpacity>
                                    ))}

                                    {activeDropdownMenu === 'Reports' && reportDropdownOptions.map(option => (
                                        <TouchableOpacity
                                            key={option}
                                            onPress={() => handleLogisticsOptionPress(option)}
                                            style={styles.logisticsOption}
                                        >
                                            <Text style={styles.logisticsOptionText}>{option}</Text>
                                        </TouchableOpacity>
                                    ))}
                                </ScrollView>
                            </View>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>

            {/* Exit Modal */}
            <Modal transparent visible={showExitModal} animationType="fade">
                <TouchableWithoutFeedback onPress={() => setShowExitModal(false)}>
                    <View style={styles.overlay}>
                        <TouchableWithoutFeedback>
                            <View style={styles.exitModalContainer}>
                                <Text style={styles.exitModalText}>Are you sure you want to exit?</Text>
                                <View style={styles.modalButtonRow}>
                                    <TouchableOpacity style={styles.modalButton} onPress={() => {
                                        setShowExitModal(false);
                                        navigation.navigate('Login');
                                    }}>
                                        <Text style={styles.modalButtonText}>Yes</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[styles.modalButton, { backgroundColor: '#aaa' }]} onPress={() => setShowExitModal(false)}>
                                        <Text style={styles.modalButtonText}>No</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 0,
    },
    appBar: {
        backgroundColor: '#02096A',
        height: 120,
        paddingTop: 20,
        paddingBottom: 0,
    },
    topRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        marginBottom: 10,
    },
    topText: { color: 'white', fontWeight: 'bold', fontSize: 15 },
    menuRow: { paddingHorizontal: 5, alignItems: 'center' },
    menuButton: { paddingHorizontal: 10, paddingVertical: 5, marginHorizontal: 5, borderRadius: 5 },
    menuText: { fontSize: 25, fontWeight: 'bold' },
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.73)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dropdownContainer: {
        position: 'absolute',
        backgroundColor: '#FDC500',
        marginTop: 10,
        padding: 10,
        minWidth: 250,
        borderRadius: 8,
        elevation: 6,
        maxHeight: 400,
    },
    dropdownItem: {
        paddingVertical: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    dropdownText: { fontSize: 16, fontWeight: '600' },
    expandArrow: { fontSize: 16 },
    subOption: { paddingLeft: 20, paddingVertical: 6 },
    subOptionText: { fontSize: 14 },
    logisticsOption: {
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(0,0,0,0.1)',
    },
    logisticsOptionText: {
        fontSize: 16,
        fontWeight: '500',
    },
    exitModalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderRadius: 8,
        elevation: 10,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -150 }, { translateY: -100 }],
        width: 300,
        height: 200,
    },
    exitModalText: { fontSize: 18, marginBottom: 20, textAlign: 'center' },
    modalButtonRow: { flexDirection: 'row', justifyContent: 'space-around', width: '100%' },
    modalButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        backgroundColor: '#FDC500',
        borderRadius: 5,
        marginHorizontal: 10,
    },
    modalButtonText: { fontSize: 16, fontWeight: 'bold' },
});

export default Navbar;