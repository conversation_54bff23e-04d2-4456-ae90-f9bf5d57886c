// SaveBillingAPI.js

export async function saveBillingData({
    token,
    branchId,
    businessChannel = "",
        deliveryMode="",
            tripID="",
    saleType = "",
    CustomerType = "",
    CustomerID = "",
    CustomerName = "",
    CustomerMobile = "",
    addressID = "",
    customerAddress = "",
    houseNo = "",
    altPhone = "",
    building = "",
    pincode = "",
    placeID = "",
    cityID = "",
    landmark = "",
    placeName = "",
    cityName = "",
    stateName = "",
    transTypeID = "",
    transSubTypeID = "",
    TimeSlotID = "",
    items = [],
    paymentMethodId = "",
    paymentGatewayId = "",
    paymentGatewayName = "",
    deliveryCharge = 0,
    remarks = "",
    selectedDiscountType = "",
    loginBranchId = "",
    loginUserID = ""
}) {
    const apiUrl = "https://retailuat.abisaio.com:9001/Api/POS";
    const businessDayApiUrl = `https://retailuat.abisibg.com/api/v1/currentbusinessday?BranchId=${branchId}`;


    // Fetch current business day
    let businessDayTimestamp = "";
    console.log("Fetching Business Date from URL:", businessDayApiUrl);
    try {
        const businessDayResponse = await fetch(businessDayApiUrl, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${token}`,
                "Content-Type": "application/json"
            }
        });
        if (businessDayResponse.ok) {
            const data = await businessDayResponse.json();
            console.log("Business Day API response:", data); // <-- ADD THIS

            if (Array.isArray(data) && data.length > 0 && data[0]["BusinessDateCode"]) {
                businessDayTimestamp = data[0]["BusinessDateCode"];
            } else {
                console.warn("Unexpected business date format or missing BusinessDateCode");
            }
        } else {
            console.warn("Business Day API returned non-OK status:", businessDayResponse.status);
        }

    } catch (error) {
        console.error("Error fetching business date:", error);
    }

    const systemDateTimeStamp = new Date().toISOString().split('T')[0] + "T00:00:00.000Z";

    let saleDetails = [];
    let totalProductAmount = 0;
    let totalDiscountAmount = 0;
    let totalTaxAmount = 0;
    let totalAmount = 0;

    for (let i = 0; i < items.length; i++) {
        const item = items[i];
        let productAmount = parseFloat(item["ItemTotalPrice"]);
        let ItemTaxAmount = productAmount * (parseFloat(item["TaxPercent"]) / 100);
        ItemTaxAmount = parseFloat(ItemTaxAmount.toFixed(2));

        let ItemDiscountAmount = parseFloat(item["Discount"]);
        let productRounded = parseFloat(productAmount.toFixed(3));
        let roundOffAdjustment = productRounded - productAmount;
        productAmount = productRounded;

        let discountDecimalPart = ItemDiscountAmount - Math.floor(ItemDiscountAmount);
        let discountRoundOffAmount = discountDecimalPart >= 0.50 ? (1.0 - discountDecimalPart) : -discountDecimalPart;
        ItemDiscountAmount += discountRoundOffAmount;

        let ItemTotalAmount = 0;
        if (selectedDiscountType === "Total value" || selectedDiscountType === "Total percentage") {
            ItemTotalAmount = productAmount + ItemTaxAmount - ItemDiscountAmount;
        } else {
            ItemTotalAmount = productAmount + ItemTaxAmount;
        }

        totalProductAmount += productAmount;
        totalDiscountAmount += ItemDiscountAmount;
        totalTaxAmount += ItemTaxAmount;

        console.log("------------------------");
        console.log(`Item number : ${i}`);
        console.log(`Product amount : ${productAmount}`);
        console.log(`Product Round off : ${roundOffAdjustment}`);
        console.log(`Discount amount : ${ItemDiscountAmount}`);
        console.log(`Discount Round off : ${discountRoundOffAmount}`);
        console.log(`Tax amount : ${ItemTaxAmount}`);
        console.log(`Total amount : ${ItemTotalAmount}`);

        saleDetails.push({
            lineNumber: (i + 1).toString().padStart(3, '0'),
            isSet: false,
            itemName: item["ItemName"] || "",
            isSaleReturn: false,
            nos: item['ItemAltQtyEnabled']
                ? item["ItemQuantity"].toString()
                : (item["ItemQuantity"] === 0 ? "" : item["ItemQuantity"].toString()),
            kgs: item['ItemAltQtyEnabled']
                ? item["ItemWeight"].toString()
                : (item["ItemWeight"] === 0 ? "" : item["ItemWeight"].toString()),
            batchNumber: item['BatchNo'] ? item['BatchNo'].toString() : "",
            rate: item["ItemPrice"] || 0,
            tradeDiscountRate: 0,
            tradeDiscountAmount: ItemDiscountAmount,
            taxableSubtotal: 0,
            taxAmount: ItemTaxAmount,
            lineFinalAmount: ItemTotalAmount,
            binID: "OKBIN",
            batchEnabled: false,
            taxCategoryId: item["ItemTaxCategoryID"] || "",
            taxStructureCode: "STATEGST",
            setItemId: "",
            setDiscountApportionRatio: 0,
            isSetFreeItem: false,
            isOfferItem: false,
            itemStatusId: "OK",
            lineDiscountCode: "",
            qtyUOM: "",
            altQtyUOM: "",
            discountOn: "",
            lineDiscountRate: 0,
            lineDiscountAmount: 0,
            lineDiscountType: "",
            altQty: 0,
            qty: item["ItemWeight"] !== 0 ? item["ItemWeight"] : item["ItemQuantity"],
            productAmount: item["ItemTotalPrice"] || 0,
            totalTaxPercent: item["TaxPercent"] || 0,
            deliveryCharge: deliveryCharge,
            isOverRideRate: true,
            isAmountMode: true,
            packageCnt: 0,
            sellByWeight: item['ItemSellByWeight'] || false,
            unitMRP: 134,
            isMRPSale: true,
            altQtyEnabled: item['ItemAltQtyEnabled'] || false,
            dayRate: item["ItemDayRate"] || 0,
            stockQty: 0,
            stockAltQty: 0,
            groupStockQty: 0,
            amountModeAllowed: true,
            allowMixSaleOfGroupItem: false,
            itemID: item["ItemID"] || "",
            itemCode: "",
            wScale: 0,
            stockGroupId: item["ItemStockGroupID"] || "",
            itemFamilyId: item["ItemFamilyID"] || "",
            parentSaleId: "",
            parentSaleLineNumber: "",
            deleted: "N",
            batchUseByDate: businessDayTimestamp,
            refDocType: "",
            refDocId: "",
            refLineNumber: "",
            refDocName: "",
            dml: "I",
            unitGrossWt: 0
        });
    }

    if (selectedDiscountType === "Total value" || selectedDiscountType === "Total percentage") {
        totalAmount = totalProductAmount + totalTaxAmount + deliveryCharge - totalDiscountAmount;
    } else {
        totalAmount = totalProductAmount + totalTaxAmount + deliveryCharge;
    }

    totalAmount = parseFloat(totalAmount.toFixed(2));
    let TotalDecimalPart = totalAmount - Math.floor(totalAmount);
    let TotalRoundOffAmount = TotalDecimalPart >= 0.50 ? (1.0 - TotalDecimalPart) : -TotalDecimalPart;
    TotalRoundOffAmount = parseFloat(TotalRoundOffAmount.toFixed(2));
    totalAmount += TotalRoundOffAmount;
    totalAmount = parseFloat(totalAmount.toFixed(2));

    console.log("------------------------");
    console.log(`total Product amount : ${totalProductAmount}`);
    console.log(`total Discount amount : ${totalDiscountAmount}`);
    console.log(`total Tax amount : ${totalTaxAmount}`);
    console.log(`Round off amount : ${TotalRoundOffAmount}`);
    console.log(`Total amount : ${totalAmount}`);

    const billingData = {
        saleID: "",
        branchId: branchId,
        channelId: businessChannel,
        transTypeId: "SALE",
        tranSubTypeId: "CASH",
        pricingSchemeId: saleType,
        businessDate: businessDayTimestamp,
        remarks: remarks,
        invType: "",
        customerID: "",
        posCustomerID: CustomerID,
        termsID: "",
        isO_number: "",
        delieveryMode: "",
        deliveryAddressID: "",
        billingAddressID: "",
        discountCode: "",
        discountRate: 0,
        totalDiscountAmount: totalDiscountAmount,
        productTotal: totalProductAmount,
        taxableSubtotal: 0,
        taxAmount: totalTaxAmount,
        roundOffAmount: TotalRoundOffAmount,
        totalAmount: totalAmount,
        deliveryDistance: 0,
        deliveryCharge: deliveryCharge,
        deliveryDiscount: 0,
        deliveryStatusCode: "",
        pointsEarned: 0,
        timeSlotId: "",
        tripId: tripID,
        posted: true,
        deleted: "N",
        posNumber: 0,
        createdUserID: loginUserID.toString(),
        parentSaleBranchID: branchId,
        parentSaleID: "",
        saleOrderId: "",
        isCancelledSale: true,
        createdDate: systemDateTimeStamp,
        modifiedUserID: "",
        modifiedDate: businessDayTimestamp,
        deletedUserID: "",
        deletedDate: businessDayTimestamp,
        postedUserID: "",
        postedDate: businessDayTimestamp,
        saleDetails: saleDetails,
        billDetails: [
            {
                lineNumber: "001",
                paymentGatewayName: paymentGatewayName || "",
                amount: Math.floor(totalAmount),
                refundAmount: 0,
                authCode: "",
                promoId: "",
                paymentGatewayID: paymentGatewayId || "",
                paymentMethodId: paymentMethodId || "",
                deleted: "N",
                authRequired: true,
                createdUserId: loginUserID.toString(),
                createdDate: systemDateTimeStamp,
                modifiedUserId: "",
                modifiedDate: businessDayTimestamp,
                deletedUserId: "",
                deletedDate: businessDayTimestamp,
                dml: "l",
                isCarryForward: true
            }
        ]
    };

    console.log("Billing Data Payload:", JSON.stringify(billingData, null, 2)); // <-- Add this line

    try {
        const response = await fetch(apiUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
            },
            body: JSON.stringify(billingData)
        });

        console.log("Business Day Timestamp:", businessDayTimestamp);
        console.log("Response Status:", response.status);

        const result = await response.json();
        return result;
    } catch (error) {
        console.error("Error saving billing data:", error);
        throw error;
    }

}

